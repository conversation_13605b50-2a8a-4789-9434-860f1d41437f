# 微信自动化添加好友系统 v1.0.0

## 📋 项目概述

这是一个完整的微信自动化添加好友系统，支持多微信窗口循环处理，严格按照6个步骤顺序执行，确保流程的完整性和稳定性。

## ✨ 核心特性

### 🔄 严格6步骤顺序执行
1. **窗口管理** - 激活微信窗口、调整大小、强制置顶
2. **主界面操作** - 导航到添加好友界面
3. **简单添加好友** - 输入手机号、搜索联系人
4. **图像识别添加** - 识别"添加到通讯录"按钮并点击
5. **好友申请窗口** - 填写验证信息、发送好友申请
6. **频率限制处理** - 智能检测和处理"操作过于频繁"错误

### 🖥️ 多窗口循环处理
- **单窗口完整性**：每个微信窗口必须完成全部6个步骤后才能切换到下一个窗口
- **循环处理机制**：支持多个微信窗口的循环处理
- **强制置顶**：每完成一个步骤后强制置顶当前操作的微信窗口

### 🛡️ 智能错误处理
- **异常捕获**：每个步骤都有完整的异常处理机制
- **自动重试**：关键操作失败时的自动重试逻辑
- **详细日志**：记录每个步骤的执行状态和结果
- **进度跟踪**：实时显示当前处理的微信窗口和执行步骤

### 📊 数据管理
- **Excel集成**：从Excel文件读取联系人信息，实时更新处理状态
- **配置管理**：支持超时时间、重试次数等参数配置
- **结果统计**：统计成功添加、失败、跳过的好友数量
- **北京时间**：所有操作都使用实时的北京在线时间

## 📁 文件结构

```
微信自动化系统/
├── main_controller.py          # 主控制器（核心程序）
├── step_executor.py           # 步骤执行器
├── run_wechat_automation.py   # 启动脚本
├── config_validator.py        # 配置验证工具
├── config.json               # 配置文件
├── 添加好友名单.xlsx          # 联系人数据文件
├── modules/                  # 核心模块目录
│   ├── window_manager.py     # 窗口管理模块
│   ├── main_interface.py     # 主界面操作模块
│   ├── wechat_auto_add_simple.py  # 简单添加好友模块
│   ├── wechat_auto_add_friend.py  # 图像识别添加模块
│   ├── friend_request_window.py   # 好友申请窗口模块
│   ├── frequency_error_handler.py # 频率限制处理模块
│   ├── data_manager.py       # 数据管理模块
│   └── ...                   # 其他支持模块
├── logs/                     # 日志目录
│   └── current/             # 当前日志
└── README.md                # 本文档
```

## 🚀 快速开始

### 1. 环境准备

确保已安装Python 3.7+和必要的依赖包：

```bash
pip install pandas openpyxl pyautogui opencv-python pillow pywin32 psutil
```

### 2. 配置Excel文件

确保 `添加好友名单.xlsx` 包含以下列：

| 列名 | 说明 | 示例 |
|------|------|------|
| 手机号码 | 待添加好友的手机号 | 13800138000 |
| 姓名 | 联系人姓名 | 张三 |
| 准考证 | 准考证号（用于生成备注） | 202301010001 |
| 验证信息 | 添加好友时的验证消息 | 您好，我想加您为好友 |
| 处理状态 | 处理状态（自动更新） | 待处理/成功/失败 |
| 处理结果 | 处理结果详情（自动更新） | 添加成功 |
| 处理时间 | 处理时间（自动更新） | 2025-01-28 10:30:00 |
| 微信窗口 | 处理该联系人的微信窗口编号（自动更新） | 1 |
| 重试次数 | 重试次数统计（自动更新） | 0 |

### 3. 验证配置

运行配置验证工具：

```bash
python config_validator.py
```

### 4. 启动程序

使用启动脚本运行：

```bash
python run_wechat_automation.py
```

或直接运行主控制器：

```bash
python main_controller.py
```

## 📖 使用说明

### 运行前准备

1. **微信准备**：
   - 确保微信已登录并处于正常状态
   - 建议关闭微信的自动更新和消息提醒
   - 确保网络连接稳定

2. **数据准备**：
   - 检查Excel文件格式是否正确
   - 确认待处理联系人数据完整
   - 备份重要数据

3. **环境准备**：
   - 关闭可能干扰的其他程序
   - 确保屏幕分辨率稳定
   - 建议使用有线网络连接

### 执行流程

1. **程序启动**：运行启动脚本，系统会自动检查环境和配置
2. **窗口检测**：自动检测所有微信窗口
3. **数据加载**：从Excel文件加载待处理联系人
4. **循环处理**：按窗口顺序执行6步骤流程
5. **结果统计**：实时更新处理结果到Excel文件
6. **完成报告**：生成详细的执行报告

### 监控和控制

- **实时日志**：查看 `logs/current/` 目录下的日志文件
- **进度监控**：控制台会显示实时进度和状态
- **中断程序**：按 `Ctrl+C` 可以安全中断程序
- **结果查看**：处理结果实时更新到Excel文件中

## ⚙️ 配置说明

### 主要配置项

- **optimized_coordinates**：界面元素坐标配置
- **window_management**：窗口管理设置
- **batch_processing**：批处理配置
- **mouse_optimization**：鼠标操作优化
- **logging**：日志配置

### 性能调优

- **contacts_per_window**：每个窗口处理的联系人数量（默认5个）
- **retry_times**：失败重试次数（默认3次）
- **delay_range**：操作间延迟范围（默认1.5-3.0秒）
- **batch_size**：批处理大小（默认10个）

## 🔧 故障排除

### 常见问题

1. **微信窗口未找到**
   - 确保微信已启动并登录
   - 检查窗口标题是否为"微信"
   - 尝试重启微信程序

2. **坐标点击不准确**
   - 检查屏幕分辨率设置
   - 运行配置验证工具
   - 调整config.json中的坐标配置

3. **Excel文件读取失败**
   - 确保文件未被其他程序占用
   - 检查文件格式和列名
   - 验证数据完整性

4. **频繁操作限制**
   - 程序会自动处理频率限制
   - 适当增加操作间延迟
   - 分批处理联系人

### 日志分析

查看日志文件获取详细错误信息：
- `logs/current/main_controller_*.log` - 主控制器日志
- `logs/current/step_executor_*.log` - 步骤执行器日志

## 📊 性能指标

- **窗口处理成功率**：≥95%
- **联系人处理成功率**：≥80%（取决于网络和微信状态）
- **平均处理时间**：每个联系人约30-60秒
- **错误恢复率**：≥90%

## 🔒 安全说明

- 程序仅在本地运行，不会上传任何数据
- 所有操作都通过模拟用户界面操作完成
- 支持随时中断和恢复
- 自动备份重要数据

## 📝 更新日志

### v1.0.0 (2025-01-28)
- ✅ 实现严格6步骤顺序执行
- ✅ 支持多微信窗口循环处理
- ✅ 集成智能错误处理机制
- ✅ 完善数据管理和统计功能
- ✅ 添加配置验证和优化工具
- ✅ 提供用户友好的启动界面

## 🤝 技术支持

如遇到问题，请：
1. 查看日志文件获取详细错误信息
2. 运行配置验证工具检查环境
3. 参考故障排除章节
4. 检查Excel文件和配置文件格式

---

**注意**：本程序仅供学习和研究使用，请遵守相关法律法规和微信使用条款。
