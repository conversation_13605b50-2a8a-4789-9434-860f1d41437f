#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新功能演示脚本
展示运行参数配置、自动休息配置和运行状态显示功能
"""

import json
import tkinter as tk
from tkinter import ttk, messagebox

def show_config_demo():
    """展示配置文件中的新功能"""
    try:
        with open("config.json", "r", encoding="utf-8") as f:
            config = json.load(f)
        
        # 创建演示窗口
        demo_window = tk.Tk()
        demo_window.title("微信自动化新功能演示")
        demo_window.geometry("600x500")
        
        # 创建笔记本控件
        notebook = ttk.Notebook(demo_window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 1. 运行参数配置演示
        params_frame = ttk.Frame(notebook)
        notebook.add(params_frame, text="运行参数配置")
        
        params_text = tk.Text(params_frame, wrap=tk.WORD, font=('Consolas', 10))
        params_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        runtime_params = config.get("runtime_parameters", {})
        params_content = f"""
🔧 运行参数配置模块

📊 单次添加间隔:
   最小值: {runtime_params.get('single_add_interval', {}).get('min', 'N/A')} 秒
   最大值: {runtime_params.get('single_add_interval', {}).get('max', 'N/A')} 秒

📈 每日添加上限: {runtime_params.get('daily_add_limit', {}).get('value', 'N/A')} 个

🪟 每窗口最大添加数量: {runtime_params.get('max_adds_per_window', {}).get('value', 'N/A')} 个

⏰ 执行时间段设置:
   上午时段: {runtime_params.get('execution_time_slots', {}).get('morning', {}).get('start', 'N/A')} - {runtime_params.get('execution_time_slots', {}).get('morning', {}).get('end', 'N/A')}
   下午时段: {runtime_params.get('execution_time_slots', {}).get('afternoon', {}).get('start', 'N/A')} - {runtime_params.get('execution_time_slots', {}).get('afternoon', {}).get('end', 'N/A')}

✨ 特性:
• 所有参数都可以通过GUI界面实时调整
• 参数修改后立即生效，无需重启程序
• 内置参数验证，确保输入值的合理性
• 支持保存到配置文件，下次启动自动加载
        """
        params_text.insert(tk.END, params_content)
        params_text.config(state=tk.DISABLED)
        
        # 2. 自动休息配置演示
        rest_frame = ttk.Frame(notebook)
        notebook.add(rest_frame, text="自动休息配置")
        
        rest_text = tk.Text(rest_frame, wrap=tk.WORD, font=('Consolas', 10))
        rest_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        rest_config = config.get("auto_rest_config", {})
        rest_content = f"""
😴 自动休息配置模块

🎯 休息触发条件:
   每添加 {rest_config.get('rest_trigger', {}).get('friends_count', 'N/A')} 个好友后自动休息

⏱️ 休息时长:
   {rest_config.get('rest_duration', {}).get('minutes', 'N/A')} 分钟

🔄 启用状态: {'✅ 已启用' if rest_config.get('enabled', False) else '❌ 已禁用'}

✨ 特性:
• 智能休息机制，避免操作过于频繁
• 可自定义休息触发条件和时长
• 休息期间显示倒计时，用户可随时了解剩余时间
• 支持手动跳过休息或延长休息时间
        """
        rest_text.insert(tk.END, rest_content)
        rest_text.config(state=tk.DISABLED)
        
        # 3. 运行状态显示演示
        status_frame = ttk.Frame(notebook)
        notebook.add(status_frame, text="运行状态显示")
        
        status_text = tk.Text(status_frame, wrap=tk.WORD, font=('Consolas', 10))
        status_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        status_config = config.get("runtime_status", {})
        status_content = f"""
📊 运行状态显示模块

📈 实时显示信息:
• 总进度: 当前完成数/总计划数 (百分比)
• 计划处理: 总数量统计
• 当前进度: 已处理数量
• 成功添加: 成功次数统计 (绿色显示)
• 失败/错误: 失败次数统计 (红色显示)
• 当前微信: 当前窗口/总窗口数
• 操作倒计时: 下次操作剩余秒数 (橙色显示)

⚙️ 配置参数:
   更新间隔: {status_config.get('update_interval', 'N/A')} 毫秒
   显示格式: {status_config.get('display_format', {})}

✨ 特性:
• 实时更新，无需手动刷新
• 彩色显示，重要信息一目了然
• 进度条可视化，直观显示完成度
• 详细统计信息，便于监控执行效果
• 倒计时功能，预知下次操作时间
        """
        status_text.insert(tk.END, status_content)
        status_text.config(state=tk.DISABLED)
        
        # 4. 使用说明
        help_frame = ttk.Frame(notebook)
        notebook.add(help_frame, text="使用说明")
        
        help_text = tk.Text(help_frame, wrap=tk.WORD, font=('Consolas', 10))
        help_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        help_content = """
📖 新功能使用说明

🚀 如何使用新功能:

1️⃣ 启动程序:
   python wechat_automation_gui.py

2️⃣ 配置运行参数:
   • 点击 "⚙️ 运行参数" 标签页
   • 调整各项参数（间隔时间、添加上限等）
   • 参数修改后立即生效
   • 点击 "保存配置" 保存到文件

3️⃣ 监控运行状态:
   • 点击 "📊 运行状态" 标签页
   • 实时查看执行进度和统计信息
   • 观察倒计时了解下次操作时间

4️⃣ 自动休息功能:
   • 在运行参数中设置休息条件
   • 程序会自动在达到条件时休息
   • 休息期间显示倒计时

🔧 技术特点:
• 所有参数都支持实时调整
• 内置参数验证机制
• 配置持久化保存
• 界面友好，操作简单
• 实时状态监控

⚠️ 注意事项:
• 参数修改后会立即应用到运行中的程序
• 建议在开始自动化前先验证参数
• 时间格式必须为 HH:MM (如 10:30)
• 数值参数必须为正整数
        """
        help_text.insert(tk.END, help_content)
        help_text.config(state=tk.DISABLED)
        
        # 添加关闭按钮
        button_frame = ttk.Frame(demo_window)
        button_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(button_frame, text="关闭演示", command=demo_window.destroy).pack(side=tk.RIGHT)
        ttk.Button(button_frame, text="启动主程序", command=lambda: launch_main_gui(demo_window)).pack(side=tk.RIGHT, padx=(0, 10))
        
        demo_window.mainloop()
        
    except Exception as e:
        messagebox.showerror("错误", f"演示失败: {e}")

def launch_main_gui(parent_window):
    """启动主GUI程序"""
    try:
        parent_window.destroy()
        import subprocess
        subprocess.Popen(["python", "wechat_automation_gui.py"])
    except Exception as e:
        messagebox.showerror("错误", f"启动主程序失败: {e}")

if __name__ == "__main__":
    show_config_demo()
