# 微信自动化添加好友 - 图形用户界面

## 📋 概述

本项目为 `main_controller.py` 微信自动化添加好友程序提供了完整的图形用户界面（GUI），让用户能够通过直观的可视化界面来控制和监控自动化流程。

## ✨ 主要特性

### 🎮 核心功能
- **一键启动/停止**：简单的按钮控制自动化流程
- **实时监控**：实时显示执行状态、进度和统计信息
- **多窗口管理**：可视化管理多个微信窗口
- **日志查看**：实时查看详细的执行日志
- **配置管理**：友好的配置编辑界面

### 🖥️ 界面特性
- **多标签页设计**：清晰的功能分区
- **响应式布局**：支持窗口大小调整
- **实时更新**：状态和进度实时刷新
- **错误处理**：友好的错误提示和处理

## 🚀 快速开始

### 1. 环境要求
- Python 3.7+
- tkinter（通常随Python安装）
- 完整的项目文件（main_controller.py、modules目录等）

### 2. 启动方式

#### 方式一：使用启动脚本（推荐）
```bash
python run_gui.py
```

#### 方式二：直接启动GUI
```bash
python wechat_automation_gui.py
```

### 3. 首次使用
1. 启动GUI后，首先检查配置设置
2. 在"控制面板"中选择Excel文件
3. 配置执行参数
4. 点击"开始自动化"按钮

## 📱 界面说明

### 🎮 控制面板
**功能**：主要的控制和状态显示区域

**组件**：
- **文件选择**：选择包含联系人信息的Excel文件
- **执行参数**：配置批次大小、操作延迟等参数
- **控制按钮**：开始、停止、暂停自动化流程
- **实时状态**：显示当前执行状态和快速统计

**使用方法**：
1. 点击"浏览"按钮选择Excel文件
2. 调整批次大小和延迟时间
3. 点击"▶ 开始自动化"启动流程
4. 观察实时状态显示

### 📈 进度监控
**功能**：详细的进度跟踪和统计信息

**组件**：
- **总体进度**：联系人处理进度条和窗口处理进度条
- **详细统计**：包含各项统计数据的表格
- **百分比显示**：直观的完成度显示

**使用方法**：
- 自动更新，无需手动操作
- 可以实时查看处理进度
- 统计表格显示详细的数量和百分比

### 🪟 窗口监控
**功能**：管理和监控微信窗口

**组件**：
- **窗口列表**：显示所有检测到的微信窗口
- **窗口信息**：窗口标题、句柄、状态等
- **操作按钮**：刷新、激活、关闭窗口

**使用方法**：
1. 点击"刷新窗口列表"更新窗口信息
2. 选择窗口后点击"激活选中窗口"
3. 必要时可以关闭有问题的窗口

### 📝 日志
**功能**：实时查看详细的执行日志

**组件**：
- **日志级别选择**：过滤不同级别的日志
- **日志显示区域**：滚动文本区域显示日志
- **日志控制**：清空、保存日志功能
- **自动滚动**：可选的自动滚动到最新日志

**使用方法**：
1. 选择要查看的日志级别
2. 勾选"自动滚动"跟踪最新日志
3. 使用"保存日志"导出日志文件

### ⚙ 配置
**功能**：编辑和管理配置文件

**组件**：
- **配置编辑器**：直接编辑JSON配置文件
- **配置按钮**：加载、保存、重置配置
- **语法高亮**：便于编辑JSON格式

**使用方法**：
1. 点击"加载配置"读取当前配置
2. 在编辑器中修改配置
3. 点击"保存配置"应用更改

## 🔧 高级功能

### 配置对话框
使用 `config_dialog.py` 提供的友好配置界面：

```python
from config_dialog import show_config_dialog
result = show_config_dialog(parent_window)
```

### 自定义日志处理
GUI集成了自定义日志处理器，可以：
- 实时显示日志信息
- 按级别过滤日志
- 彩色显示不同级别的日志

### 多线程处理
- 自动化流程在后台线程运行
- GUI界面保持响应
- 通过消息队列进行线程间通信

## 🛠️ 故障排除

### 常见问题

**1. GUI启动失败**
- 检查Python版本（需要3.7+）
- 确保tkinter已安装
- 检查项目文件完整性

**2. 无法找到微信窗口**
- 确保微信已启动并登录
- 检查窗口管理器配置
- 尝试刷新窗口列表

**3. 配置保存失败**
- 检查JSON格式是否正确
- 确保有文件写入权限
- 检查配置文件路径

**4. 自动化流程异常**
- 查看日志标签页的详细错误信息
- 检查Excel文件格式
- 验证配置文件设置

### 调试技巧

**1. 启用详细日志**
```python
# 在配置文件中设置
"logging": {
    "level": "DEBUG"
}
```

**2. 检查环境**
```bash
python run_gui.py
```
启动脚本会自动检查环境和依赖。

**3. 手动测试**
可以先运行原始的 `main_controller.py` 确保核心功能正常。

## 📁 文件结构

```
├── wechat_automation_gui.py    # 主GUI程序
├── run_gui.py                  # GUI启动脚本
├── config_dialog.py            # 配置对话框
├── main_controller.py          # 核心控制器
├── config.json                 # 配置文件
├── modules/                    # 功能模块目录
└── logs/                       # 日志目录
    ├── current/               # 当前日志
    ├── archive/               # 归档日志
    └── gui/                   # GUI日志
```

## 🔄 更新和维护

### 配置更新
- 通过GUI的配置标签页直接编辑
- 或使用配置对话框进行友好编辑
- 修改后需要重启程序生效

### 日志管理
- 日志自动按日期和类型分类
- 可以通过GUI导出日志文件
- 定期清理过期日志文件

### 功能扩展
GUI采用模块化设计，可以轻松添加新功能：
- 新增标签页
- 扩展配置选项
- 添加新的监控指标

## 📞 技术支持

如果遇到问题：
1. 查看GUI中的日志信息
2. 检查 `logs/` 目录下的详细日志
3. 验证配置文件格式
4. 确保所有依赖文件存在

---

**版本**：v1.0.0  
**创建时间**：2025-01-28  
**兼容性**：Python 3.7+, Windows 10+
