{"excel_file": "添加好友名单.xlsx", "log_file": "logs/current/wechat_auto.log", "delay_range": [1.5, 3.0], "click_delay": [0.3, 0.8], "retry_times": 3, "batch_size": 50, "window_position": [0, 0], "window_management": {"auto_move_to_top_left": true, "move_main_window": true, "move_dialog_window": true, "resize_main_window": true, "main_window_size": {"width": 723, "height": 650}}, "ui_elements": {"wechat_button": {"text": "微信", "timeout": 5}, "contacts_button": {"text": "通讯录", "timeout": 5}, "add_friend_button": {"text": "添加朋友", "timeout": 5}, "search_input": {"class": "EditWrapper", "timeout": 5}, "search_button": {"text": "搜索", "timeout": 5}, "add_button": {"text": "添加到通讯录", "timeout": 5}, "send_button": {"text": "发送", "timeout": 5}}, "optimized_coordinates": {"微信按钮": [31, 95], "通讯录按钮": [29, 144], "微信主按钮": [31, 95], "+快捷操作按钮": [244, 41], "添加朋友选项": [252, 125], "搜索输入框": [130, 77], "搜索按钮": [280, 77]}, "mouse_optimization": {"slow": {"duration": 0.5, "pause": 0.2, "click_delay": 0.3}, "medium": {"duration": 0.3, "pause": 0.1, "click_delay": 0.2}, "fast": {"duration": 0.1, "pause": 0.05, "click_delay": 0.1}, "ultra_fast": {"duration": 0.05, "pause": 0.02, "click_delay": 0.05}}, "ocr_keywords": {"add_to_contacts": ["添加到通讯录", "添加到联系人", "加入通讯录", "添加联系人", "add to contacts", "add contact"], "user_not_found": ["无法找到该用户", "请检查你填写的账号是否正确", "用户不存在", "账号不存在", "找不到用户", "用户未找到", "user not found", "account not found", "无法找到", "不存在该用户"], "chat_options": ["发消息", "语音聊天", "视频聊天", "发送消息", "语音通话", "视频通话", "send message", "voice chat", "video chat", "聊天", "通话"]}, "red_border_highlight": {"enabled": true, "border_width": 3, "border_color": "red", "display_time": 1.5, "element_padding": 10, "auto_detect_size": true, "default_size": [80, 30], "element_sizes": {"微信按钮": [45, 20], "通讯录按钮": [45, 20], "微信主按钮": [45, 20], "+快捷操作按钮": [30, 30], "添加朋友选项": [70, 25], "添加到通讯录": [100, 30], "搜索输入框": [200, 28], "搜索按钮": [60, 28]}}, "error_messages": {"user_not_found": ["无法找到该用户", "用户不存在", "该用户不存在", "找不到该用户"], "already_friend": ["已经是好友", "对方已经是你的朋友", "该用户已经是你的好友"], "too_frequent": ["添加频繁", "请稍后再试", "操作过于频繁", "操作太频繁", "操作频繁"], "network_error": ["网络连接失败", "网络异常", "连接超时", "网络错误"], "permission_denied": ["权限不足", "无权限操作", "没有权限"]}, "smart_detection": {"enabled": true, "description": "智能检测和处理操作频繁对话框配置", "detection_methods": {"child_controls": {"enabled": true, "priority": 1, "description": "通过子控件检测确定按钮"}, "text_recognition": {"enabled": true, "priority": 2, "description": "通过文本识别检测确定按钮", "ocr_enabled": false, "keywords": ["确定", "OK", "确认", "Confirm", "是", "Yes"]}, "image_recognition": {"enabled": true, "priority": 3, "description": "通过图像识别检测确定按钮", "edge_detection": true, "template_matching": false}, "relative_position": {"enabled": true, "priority": 4, "description": "基于相对位置计算确定按钮坐标", "default_ratios": {"x_ratio": 0.7, "y_ratio": 0.8}, "specific_sizes": {"330x194": {"x_ratio": 0.75, "y_ratio": 0.75}}}}, "click_methods": {"win32_api": {"enabled": true, "priority": 1, "description": "使用Win32 API点击"}, "pyautogui": {"enabled": true, "priority": 2, "description": "使用PyAutoGUI点击"}, "keyboard_enter": {"enabled": true, "priority": 3, "description": "使用键盘Enter键"}, "window_message": {"enabled": true, "priority": 4, "description": "发送窗口消息"}}, "retry_settings": {"max_detection_attempts": 3, "max_click_attempts": 4, "detection_interval": 0.5, "click_interval": 0.8, "verification_timeout": 2.0}, "compatibility": {"cross_resolution": true, "window_position_independent": true, "fallback_to_coordinates": true, "default_coordinates": [1364, 252]}}, "safety": {"max_operations_per_hour": 100, "max_runtime_hours": 8, "cool_down_time": 300, "enable_mouse_trace": true, "human_like_delay": true, "frequent_operation_detection": true, "auto_stop_on_warning": true}, "batch_processing": {"enabled": true, "batch_size": 10, "rest_time_minutes": 5, "rest_time_seconds": 300, "show_countdown": true, "countdown_interval": 30}, "automation": {"auto_start": true, "startup_delay": 3, "auto_exit": true, "exit_delay": 2, "skip_confirmations": true}, "accounts": [{"name": "主账号", "type": "default", "description": "默认微信账号", "enabled": true}], "account_switch_delay": 10, "multi_window": {"enabled": true, "contacts_per_window": 2, "switch_delay": 3, "contact_delay": 1.5, "max_windows": 5, "window_validation": true, "fallback_to_single": true}, "news_popup_blocker": {"enabled": true, "description": "微信新闻提示框阻止器配置", "detection_interval": 2.0, "auto_close_delay": 0.5, "max_detection_attempts": 3, "characteristics": {"class_names": ["Qt51514QWindowIcon", "Qt5QWindowIcon", "Qt6QWindowIcon"], "title_keywords": ["新闻", "消息", "通知", "推送", "资讯", "头条", "热点"], "title_exclusions": ["微信", "聊天", "添加朋友", "申请添加朋友", "通讯录"], "size_range": {"min_width": 200, "max_width": 600, "min_height": 150, "max_height": 400}, "position_hints": ["bottom_right", "near_wechat_window"]}, "advanced_settings": {"enable_for_window_2": true, "enable_for_all_windows": false, "delayed_detection_times": [1.0, 2.0, 3.0], "monitor_after_switch": true, "close_method": "graceful", "fallback_methods": ["forceful", "hide"], "log_blocked_popups": true}}, "logging": {"level": "INFO", "max_file_size": "10MB", "backup_count": 5, "retention_days": 30, "auto_cleanup": true, "cleanup_time": "02:00", "archive_path": "logs/archive", "temp_path": "logs/temp", "current_path": "logs/current", "deduplication": {"enabled": true, "max_files_per_type": 1, "preserve_latest": true, "backup_before_delete": true, "type_specific_limits": {"friend_request_processor": 1, "wechat_auto": 2, "data_manager": 1, "main_controller": 1, "window_manager": 1}}}, "runtime_parameters": {"description": "运行参数配置模块", "single_add_interval": {"min": 50, "max": 60, "unit": "seconds", "description": "单次添加间隔范围"}, "daily_add_limit": {"value": 20, "description": "每日添加上限"}, "max_adds_per_window": {"value": 20, "description": "每窗口最大添加数量，0表示不限制"}, "execution_time_slots": {"morning": {"start": "10:00", "end": "12:00", "enabled": true, "description": "上午执行时段"}, "afternoon": {"start": "14:00", "end": "23:59", "enabled": true, "description": "下午执行时段"}}}, "auto_rest_config": {"description": "自动休息配置模块", "rest_trigger": {"friends_count": 20, "description": "每添加X个好友后休息"}, "rest_duration": {"minutes": 5, "description": "休息时长（分钟）"}, "enabled": true}, "runtime_status": {"description": "运行状态显示配置", "update_interval": 1000, "display_format": {"progress_percentage": true, "countdown_timer": true, "real_time_stats": true}}}