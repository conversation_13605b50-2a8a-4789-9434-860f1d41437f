#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GUI改进效果的简化版本
"""

import tkinter as tk
from tkinter import ttk

class TestGUIImprovements:
    """测试GUI改进效果"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("微信自动化GUI改进测试")
        self.root.geometry("800x600")

        # 测试变量
        self.test_vars = {
            "interval_min": tk.StringVar(value="50"),
            "interval_max": tk.StringVar(value="60"),
            "daily_limit": tk.StringVar(value="200"),
            "batch_size": tk.StringVar(value="10"),
            "delay": tk.StringVar(value="2.0")
        }

        # 设置样式
        self.setup_styles()

        # 创建测试界面
        self.create_test_interface()
        
    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # 自定义样式 - 增大字体大小，改善颜色对比度
        style.configure('Title.TLabel', font=('Arial', 14, 'bold'), foreground='#2c3e50')
        style.configure('Status.TLabel', font=('Arial', 12), foreground='#34495e')
        style.configure('Success.TLabel', foreground='#27ae60', font=('Arial', 12, 'bold'))
        style.configure('Error.TLabel', foreground='#e74c3c', font=('Arial', 12, 'bold'))
        style.configure('Warning.TLabel', foreground='#f39c12', font=('Arial', 12, 'bold'))
        style.configure('Large.TLabel', font=('Arial', 13), foreground='#2c3e50')
        style.configure('Medium.TLabel', font=('Arial', 11), foreground='#34495e')
        
        # 按钮样式
        style.configure('Large.TButton', font=('Arial', 11))
        
        # 输入框样式
        style.configure('Large.TEntry', font=('Arial', 11))
        
    def create_test_interface(self):
        """创建测试界面"""
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题
        title_label = ttk.Label(main_frame, text="GUI改进效果测试", style='Title.TLabel')
        title_label.pack(pady=(0, 20))
        
        # 测试区域1：带增减按钮的数值输入
        test_frame1 = ttk.LabelFrame(main_frame, text="📊 数值输入控件测试", padding=15)
        test_frame1.pack(fill=tk.X, pady=(0, 15))
        
        # 间隔最小值
        min_frame = ttk.Frame(test_frame1)
        min_frame.pack(fill=tk.X, pady=(0, 8))
        ttk.Label(min_frame, text="最小间隔(秒):", style='Medium.TLabel').pack(side=tk.LEFT)
        
        min_control_frame = ttk.Frame(min_frame)
        min_control_frame.pack(side=tk.RIGHT)
        
        ttk.Button(min_control_frame, text="-", width=3,
                  command=lambda: self.adjust_numeric_value(self.test_vars["interval_min"], -1, 1, 300)).pack(side=tk.LEFT)
        min_entry = ttk.Entry(min_control_frame, textvariable=self.test_vars["interval_min"], width=8, style='Large.TEntry')
        min_entry.pack(side=tk.LEFT, padx=2)
        ttk.Button(min_control_frame, text="+", width=3,
                  command=lambda: self.adjust_numeric_value(self.test_vars["interval_min"], 1, 1, 300)).pack(side=tk.LEFT)
        
        # 每日限制
        daily_frame = ttk.Frame(test_frame1)
        daily_frame.pack(fill=tk.X, pady=(0, 8))
        ttk.Label(daily_frame, text="每日添加上限:", style='Medium.TLabel').pack(side=tk.LEFT)
        
        daily_control_frame = ttk.Frame(daily_frame)
        daily_control_frame.pack(side=tk.RIGHT)
        
        ttk.Button(daily_control_frame, text="-", width=3,
                  command=lambda: self.adjust_numeric_value(self.test_vars["daily_limit"], -10, 10, 1000)).pack(side=tk.LEFT)
        daily_entry = ttk.Entry(daily_control_frame, textvariable=self.test_vars["daily_limit"], width=8, style='Large.TEntry')
        daily_entry.pack(side=tk.LEFT, padx=2)
        ttk.Button(daily_control_frame, text="+", width=3,
                  command=lambda: self.adjust_numeric_value(self.test_vars["daily_limit"], 10, 10, 1000)).pack(side=tk.LEFT)
        
        # 测试区域2：状态显示
        test_frame2 = ttk.LabelFrame(main_frame, text="📈 状态显示测试", padding=15)
        test_frame2.pack(fill=tk.X, pady=(0, 15))
        
        # 状态项
        self.create_status_item(test_frame2, "当前状态:", "运行中", '#27ae60')
        self.create_status_item(test_frame2, "处理进度:", "150/200", '#3498db')
        self.create_status_item(test_frame2, "错误计数:", "3", '#e74c3c')
        
        # 测试区域3：按钮
        test_frame3 = ttk.LabelFrame(main_frame, text="🎮 按钮测试", padding=15)
        test_frame3.pack(fill=tk.X, pady=(0, 15))
        
        button_frame = ttk.Frame(test_frame3)
        button_frame.pack()
        
        ttk.Button(button_frame, text="开始", style='Large.TButton').pack(side=tk.LEFT, padx=(0, 8))
        ttk.Button(button_frame, text="暂停", style='Large.TButton').pack(side=tk.LEFT, padx=(0, 8))
        ttk.Button(button_frame, text="停止", style='Large.TButton').pack(side=tk.LEFT)
        
        # 说明文字
        info_frame = ttk.Frame(main_frame)
        info_frame.pack(fill=tk.X, pady=(15, 0))
        
        info_text = """改进说明：
1. ✅ 增大了所有文字的字体大小（从8-10号增加到11-14号）
2. ✅ 改善了文字颜色对比度，使用更清晰的颜色方案
3. ✅ 为数值输入框添加了+/-按钮，方便调整数值
4. ✅ 优化了布局间距，减少空白区域
5. ✅ 删除了快速统计模块（在实际文件中已完成）"""
        
        ttk.Label(info_frame, text=info_text, style='Medium.TLabel', justify=tk.LEFT).pack(anchor=tk.W)
        
    def create_status_item(self, parent, label_text, value_text, color='#2c3e50'):
        """创建状态显示项"""
        frame = ttk.Frame(parent)
        frame.pack(fill=tk.X, pady=3)

        ttk.Label(frame, text=label_text, font=('Arial', 11), foreground='#34495e').pack(side=tk.LEFT)
        status_label = ttk.Label(frame, text=value_text, font=('Arial', 11, 'bold'))
        status_label.configure(foreground=color)
        status_label.pack(side=tk.RIGHT)
        
    def adjust_numeric_value(self, var, delta, min_val, max_val):
        """调整数值变量的值"""
        try:
            current_val = int(var.get())
            new_val = max(min_val, min(max_val, current_val + delta))
            var.set(str(new_val))
            print(f"调整数值: {var.get()}")
        except ValueError:
            var.set(str(min_val))
            print(f"重置数值: {var.get()}")
            
    def run(self):
        """运行测试界面"""
        self.root.mainloop()

if __name__ == "__main__":
    app = TestGUIImprovements()
    app.run()
